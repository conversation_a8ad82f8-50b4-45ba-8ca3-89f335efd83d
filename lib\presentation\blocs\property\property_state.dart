import 'package:equatable/equatable.dart';
import '../../../data/models/property_model.dart';

enum PropertyStatus {
  initial,
  loading,
  loaded,
  error,
  refreshing,
  searching,
  updating,
  creating,
  deleting,
}

class PropertyState extends Equatable {
  final PropertyStatus status;
  final List<PropertyModel> properties;
  final List<PropertyModel> filteredProperties;
  final PropertyModel? selectedProperty;
  final PropertyMetrics? selectedPropertyMetrics;
  final String? errorMessage;
  final bool isLoading;
  final bool isRefreshing;
  final String? searchQuery;
  final String? statusFilter;
  final String? typeFilter;
  final bool hasReachedMax;
  final int currentPage;
  final bool isOffline;

  const PropertyState({
    this.status = PropertyStatus.initial,
    this.properties = const [],
    this.filteredProperties = const [],
    this.selectedProperty,
    this.selectedPropertyMetrics,
    this.errorMessage,
    this.isLoading = false,
    this.isRefreshing = false,
    this.searchQuery,
    this.statusFilter,
    this.typeFilter,
    this.hasReachedMax = false,
    this.currentPage = 1,
    this.isOffline = false,
  });

  PropertyState copyWith({
    PropertyStatus? status,
    List<PropertyModel>? properties,
    List<PropertyModel>? filteredProperties,
    PropertyModel? selectedProperty,
    PropertyMetrics? selectedPropertyMetrics,
    String? errorMessage,
    bool? isLoading,
    bool? isRefreshing,
    String? searchQuery,
    String? statusFilter,
    String? typeFilter,
    bool? hasReachedMax,
    int? currentPage,
    bool? isOffline,
  }) {
    return PropertyState(
      status: status ?? this.status,
      properties: properties ?? this.properties,
      filteredProperties: filteredProperties ?? this.filteredProperties,
      selectedProperty: selectedProperty ?? this.selectedProperty,
      selectedPropertyMetrics: selectedPropertyMetrics ?? this.selectedPropertyMetrics,
      errorMessage: errorMessage,
      isLoading: isLoading ?? this.isLoading,
      isRefreshing: isRefreshing ?? this.isRefreshing,
      searchQuery: searchQuery ?? this.searchQuery,
      statusFilter: statusFilter ?? this.statusFilter,
      typeFilter: typeFilter ?? this.typeFilter,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      isOffline: isOffline ?? this.isOffline,
    );
  }

  bool get hasError => status == PropertyStatus.error && errorMessage != null;
  bool get isEmpty => properties.isEmpty && status == PropertyStatus.loaded;
  bool get hasProperties => properties.isNotEmpty;
  bool get isSearching => searchQuery != null && searchQuery!.isNotEmpty;
  bool get hasFilters => statusFilter != null || typeFilter != null;

  List<PropertyModel> get displayProperties {
    if (filteredProperties.isNotEmpty || isSearching || hasFilters) {
      return filteredProperties;
    }
    return properties;
  }

  int get totalProperties => properties.length;
  int get activeProperties => properties.where((p) => p.status == PropertyStatus.active).length;
  int get inactiveProperties => properties.where((p) => p.status == PropertyStatus.inactive).length;
  int get maintenanceProperties => properties.where((p) => p.status == PropertyStatus.maintenance).length;
  int get criticalProperties => properties.where((p) => p.status == PropertyStatus.critical).length;

  @override
  List<Object?> get props => [
        status,
        properties,
        filteredProperties,
        selectedProperty,
        selectedPropertyMetrics,
        errorMessage,
        isLoading,
        isRefreshing,
        searchQuery,
        statusFilter,
        typeFilter,
        hasReachedMax,
        currentPage,
        isOffline,
      ];
}

// Specific state classes for different scenarios
class PropertyInitial extends PropertyState {
  const PropertyInitial() : super(status: PropertyStatus.initial);
}

class PropertyLoading extends PropertyState {
  const PropertyLoading() : super(status: PropertyStatus.loading, isLoading: true);
}

class PropertyLoaded extends PropertyState {
  const PropertyLoaded({
    required List<PropertyModel> properties,
    List<PropertyModel>? filteredProperties,
    String? searchQuery,
    String? statusFilter,
    String? typeFilter,
    bool hasReachedMax = false,
    int currentPage = 1,
    bool isOffline = false,
  }) : super(
          status: PropertyStatus.loaded,
          properties: properties,
          filteredProperties: filteredProperties ?? const [],
          searchQuery: searchQuery,
          statusFilter: statusFilter,
          typeFilter: typeFilter,
          hasReachedMax: hasReachedMax,
          currentPage: currentPage,
          isOffline: isOffline,
        );
}

class PropertyError extends PropertyState {
  const PropertyError({
    required String errorMessage,
    List<PropertyModel>? properties,
    bool isOffline = false,
  }) : super(
          status: PropertyStatus.error,
          errorMessage: errorMessage,
          properties: properties ?? const [],
          isOffline: isOffline,
        );
}

class PropertyRefreshing extends PropertyState {
  const PropertyRefreshing({
    required List<PropertyModel> properties,
    List<PropertyModel>? filteredProperties,
    String? searchQuery,
    String? statusFilter,
    String? typeFilter,
  }) : super(
          status: PropertyStatus.refreshing,
          isRefreshing: true,
          properties: properties,
          filteredProperties: filteredProperties ?? const [],
          searchQuery: searchQuery,
          statusFilter: statusFilter,
          typeFilter: typeFilter,
        );
}

class PropertySearching extends PropertyState {
  const PropertySearching({
    required List<PropertyModel> properties,
    required String searchQuery,
    String? statusFilter,
    String? typeFilter,
  }) : super(
          status: PropertyStatus.searching,
          isLoading: true,
          properties: properties,
          searchQuery: searchQuery,
          statusFilter: statusFilter,
          typeFilter: typeFilter,
        );
}

class PropertyUpdating extends PropertyState {
  const PropertyUpdating({
    required List<PropertyModel> properties,
    PropertyModel? selectedProperty,
  }) : super(
          status: PropertyStatus.updating,
          isLoading: true,
          properties: properties,
          selectedProperty: selectedProperty,
        );
}

class PropertyCreating extends PropertyState {
  const PropertyCreating({
    required List<PropertyModel> properties,
  }) : super(
          status: PropertyStatus.creating,
          isLoading: true,
          properties: properties,
        );
}

class PropertyDeleting extends PropertyState {
  const PropertyDeleting({
    required List<PropertyModel> properties,
    required String propertyId,
  }) : super(
          status: PropertyStatus.deleting,
          isLoading: true,
          properties: properties,
        );
}
