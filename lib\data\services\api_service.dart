import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:json_annotation/json_annotation.dart';

import '../models/user_model.dart';
import '../models/property_model.dart';
import '../models/fuel_model.dart';

part 'api_service.g.dart';

@RestApi()
abstract class ApiService {
  factory ApiService(Dio dio, {String baseUrl}) = _ApiService;

  // Authentication Endpoints
  @POST('/auth/login')
  Future<AuthResponse> login(@Body() LoginRequest request);

  @POST('/auth/register')
  Future<AuthResponse> register(@Body() Map<String, dynamic> request);

  @POST('/auth/refresh')
  Future<AuthResponse> refreshToken(@Body() Map<String, dynamic> request);

  @POST('/auth/logout')
  Future<void> logout();

  @GET('/auth/me')
  Future<UserModel> getCurrentUser();

  // Properties Endpoints
  @GET('/properties')
  Future<List<PropertyModel>> getProperties({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('search') String? search,
    @Query('status') String? status,
    @Query('type') String? type,
  });

  @GET('/properties/{id}')
  Future<PropertyModel> getProperty(@Path('id') String id);

  @GET('/properties/{id}/status')
  Future<PropertyMetrics> getPropertyStatus(@Path('id') String id);

  @PUT('/properties/{id}')
  Future<PropertyModel> updateProperty(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  // Generator Fuel Endpoints
  @GET('/generator-fuel/{propertyId}')
  Future<List<FuelModel>> getFuelRecords(
    @Path('propertyId') String propertyId, {
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('from') String? from,
    @Query('to') String? to,
  });

  @POST('/generator-fuel')
  Future<FuelModel> createFuelRecord(@Body() FuelCreateRequest request);

  @PUT('/generator-fuel/{id}')
  Future<FuelModel> updateFuelRecord(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  @DELETE('/generator-fuel/{id}')
  Future<void> deleteFuelRecord(@Path('id') String id);

  @GET('/generator-fuel/{propertyId}/analytics')
  Future<FuelAnalytics> getFuelAnalytics(
    @Path('propertyId') String propertyId, {
    @Query('period') String? period,
    @Query('from') String? from,
    @Query('to') String? to,
  });

  // Maintenance Endpoints
  @GET('/maintenance-issues')
  Future<List<Map<String, dynamic>>> getMaintenanceIssues({
    @Query('propertyId') String? propertyId,
    @Query('status') String? status,
    @Query('priority') String? priority,
    @Query('page') int? page,
    @Query('limit') int? limit,
  });

  @POST('/maintenance-issues')
  Future<Map<String, dynamic>> createMaintenanceIssue(
    @Body() Map<String, dynamic> request,
  );

  @PUT('/maintenance-issues/{id}')
  Future<Map<String, dynamic>> updateMaintenanceIssue(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  @GET('/maintenance-issues/{id}')
  Future<Map<String, dynamic>> getMaintenanceIssue(@Path('id') String id);

  // Attendance Endpoints
  @GET('/attendance/{siteId}')
  Future<List<Map<String, dynamic>>> getAttendanceRecords(
    @Path('siteId') String siteId, {
    @Query('date') String? date,
    @Query('userId') String? userId,
    @Query('page') int? page,
    @Query('limit') int? limit,
  });

  @POST('/attendance')
  Future<Map<String, dynamic>> recordAttendance(
    @Body() Map<String, dynamic> request,
  );

  @GET('/attendance/reports')
  Future<List<Map<String, dynamic>>> getAttendanceReports({
    @Query('propertyId') String? propertyId,
    @Query('from') String? from,
    @Query('to') String? to,
    @Query('userId') String? userId,
  });

  // OTT Services Endpoints
  @GET('/ott-services/{propertyId}')
  Future<List<Map<String, dynamic>>> getOttServices(
    @Path('propertyId') String propertyId,
  );

  @POST('/ott-services')
  Future<Map<String, dynamic>> createOttService(
    @Body() Map<String, dynamic> request,
  );

  @PUT('/ott-services/{id}')
  Future<Map<String, dynamic>> updateOttService(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  @DELETE('/ott-services/{id}')
  Future<void> deleteOttService(@Path('id') String id);

  // Dashboard Endpoints
  @GET('/dashboard/status')
  Future<Map<String, dynamic>> getDashboardStatus({
    @Query('propertyId') String? propertyId,
  });

  @GET('/dashboard/analytics')
  Future<Map<String, dynamic>> getDashboardAnalytics({
    @Query('propertyId') String? propertyId,
    @Query('period') String? period,
  });

  // Admin Endpoints
  @GET('/admin/users')
  Future<List<UserModel>> getUsers({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('role') String? role,
    @Query('search') String? search,
  });

  @POST('/admin/users')
  Future<UserModel> createUser(@Body() Map<String, dynamic> request);

  @PUT('/admin/users/{id}')
  Future<UserModel> updateUser(
    @Path('id') String id,
    @Body() Map<String, dynamic> data,
  );

  @DELETE('/admin/users/{id}')
  Future<void> deleteUser(@Path('id') String id);

  @GET('/admin/thresholds')
  Future<Map<String, dynamic>> getThresholds();

  @PUT('/admin/thresholds')
  Future<Map<String, dynamic>> updateThresholds(
    @Body() Map<String, dynamic> data,
  );

  // File Upload
  @POST('/upload')
  @MultiPart()
  Future<Map<String, dynamic>> uploadFile(
    @Part(name: 'file') List<int> file,
    @Part(name: 'type') String? type,
    @Part(name: 'propertyId') String? propertyId,
  );

  // Notifications
  @GET('/notifications')
  Future<List<Map<String, dynamic>>> getNotifications({
    @Query('page') int? page,
    @Query('limit') int? limit,
    @Query('unread') bool? unread,
  });

  @PUT('/notifications/{id}/read')
  Future<void> markNotificationAsRead(@Path('id') String id);

  @PUT('/notifications/read-all')
  Future<void> markAllNotificationsAsRead();

  // Settings
  @GET('/settings')
  Future<Map<String, dynamic>> getSettings();

  @PUT('/settings')
  Future<Map<String, dynamic>> updateSettings(
    @Body() Map<String, dynamic> data,
  );
}
