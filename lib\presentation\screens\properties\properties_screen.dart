import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../core/theme/app_theme.dart';
import '../../../data/models/property_model.dart';
import '../../blocs/property/property_bloc.dart';
import '../../blocs/property/property_event.dart';
import '../../blocs/property/property_state.dart';
import '../../widgets/property_card.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/loading_button.dart';

class PropertiesScreen extends StatefulWidget {
  const PropertiesScreen({super.key});

  @override
  State<PropertiesScreen> createState() => _PropertiesScreenState();
}

class _PropertiesScreenState extends State<PropertiesScreen> {
  final RefreshController _refreshController = RefreshController();
  final TextEditingController _searchController = TextEditingController();
  String? _selectedStatusFilter;
  String? _selectedTypeFilter;

  @override
  void initState() {
    super.initState();
    // Load properties when screen initializes
    context.read<PropertyBloc>().add(const PropertyLoadRequested());
  }

  @override
  void dispose() {
    _refreshController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onRefresh() {
    context.read<PropertyBloc>().add(const PropertyLoadRequested(forceRefresh: true));
  }

  void _onSearch(String query) {
    context.read<PropertyBloc>().add(PropertySearchRequested(query: query));
  }

  void _onFilterChanged() {
    context.read<PropertyBloc>().add(PropertyFilterChanged(
      status: _selectedStatusFilter,
      type: _selectedTypeFilter,
    ));
  }

  void _clearFilters() {
    setState(() {
      _selectedStatusFilter = null;
      _selectedTypeFilter = null;
      _searchController.clear();
    });
    context.read<PropertyBloc>().add(const PropertyFilterChanged());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Properties'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterBottomSheet,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddPropertyDialog,
          ),
        ],
      ),
      body: BlocConsumer<PropertyBloc, PropertyState>(
        listener: (context, state) {
          if (state.status == PropertyStatus.loaded || 
              state.status == PropertyStatus.error) {
            _refreshController.refreshCompleted();
          }
          
          if (state.hasError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: AppTheme.errorColor,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              // Search Bar
              _buildSearchBar(state),
              
              // Filter Chips
              if (state.hasFilters || state.isSearching)
                _buildActiveFilters(state),
              
              // Properties List
              Expanded(
                child: _buildPropertiesList(state),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchBar(PropertyState state) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SearchTextField(
        controller: _searchController,
        hintText: 'Search properties...',
        onChanged: _onSearch,
        onClear: () => _onSearch(''),
      ),
    );
  }

  Widget _buildActiveFilters(PropertyState state) {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                if (state.isSearching)
                  _buildFilterChip(
                    label: 'Search: "${state.searchQuery}"',
                    onDeleted: () {
                      _searchController.clear();
                      _onSearch('');
                    },
                  ),
                if (state.statusFilter != null)
                  _buildFilterChip(
                    label: 'Status: ${state.statusFilter}',
                    onDeleted: () {
                      setState(() => _selectedStatusFilter = null);
                      _onFilterChanged();
                    },
                  ),
                if (state.typeFilter != null)
                  _buildFilterChip(
                    label: 'Type: ${state.typeFilter}',
                    onDeleted: () {
                      setState(() => _selectedTypeFilter = null);
                      _onFilterChanged();
                    },
                  ),
              ],
            ),
          ),
          if (state.hasFilters || state.isSearching)
            TextButton(
              onPressed: _clearFilters,
              child: const Text('Clear All'),
            ),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required VoidCallback onDeleted,
  }) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(label),
        onDeleted: onDeleted,
        backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
        deleteIconColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildPropertiesList(PropertyState state) {
    if (state.status == PropertyStatus.loading && state.properties.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.isEmpty) {
      return _buildEmptyState();
    }

    return SmartRefresher(
      controller: _refreshController,
      onRefresh: _onRefresh,
      header: const WaterDropHeader(),
      child: ListView.builder(
        itemCount: state.displayProperties.length,
        itemBuilder: (context, index) {
          final property = state.displayProperties[index];
          return PropertyCard(
            property: property,
            onTap: () => _navigateToPropertyDetails(property),
            showMetrics: true,
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.business_outlined,
            size: 64,
            color: AppTheme.textSecondaryColor,
          ),
          const SizedBox(height: 16),
          const Text(
            'No Properties Found',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textColor,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Try adjusting your search or filters',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _clearFilters,
            icon: const Icon(Icons.clear),
            label: const Text('Clear Filters'),
          ),
        ],
      ),
    );
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildFilterBottomSheet(),
    );
  }

  Widget _buildFilterBottomSheet() {
    return StatefulBuilder(
      builder: (context, setModalState) {
        return Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Filter Properties',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              
              // Status Filter
              const Text(
                'Status',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['active', 'inactive', 'maintenance', 'critical']
                    .map((status) => FilterChip(
                          label: Text(status.toUpperCase()),
                          selected: _selectedStatusFilter == status,
                          onSelected: (selected) {
                            setModalState(() {
                              _selectedStatusFilter = selected ? status : null;
                            });
                          },
                        ))
                    .toList(),
              ),
              
              const SizedBox(height: 20),
              
              // Type Filter
              const Text(
                'Type',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: ['residential', 'commercial', 'industrial', 'mixed']
                    .map((type) => FilterChip(
                          label: Text(type.toUpperCase()),
                          selected: _selectedTypeFilter == type,
                          onSelected: (selected) {
                            setModalState(() {
                              _selectedTypeFilter = selected ? type : null;
                            });
                          },
                        ))
                    .toList(),
              ),
              
              const SizedBox(height: 30),
              
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        setModalState(() {
                          _selectedStatusFilter = null;
                          _selectedTypeFilter = null;
                        });
                      },
                      child: const Text('Clear'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        setState(() {
                          // Update main state
                        });
                        _onFilterChanged();
                        Navigator.pop(context);
                      },
                      child: const Text('Apply'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  void _showAddPropertyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add Property'),
        content: const Text('Property creation feature coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _navigateToPropertyDetails(PropertyModel property) {
    // TODO: Navigate to property details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening ${property.name} details...'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
